/* pages/feedback-list/feedback-list.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 任务单信息头部 */
.header {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.task-info {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.task-title {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.task-number {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.part-name {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

.task-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.strength-grade,
.scheduled-time {
  font-size: 28rpx;
  color: #666;
}

.project-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  padding-top: 10rpx;
  border-top: 1rpx solid #eee;
}

.project-name,
.construction-unit {
  font-size: 28rpx;
  color: #333;
}







/* 分类反馈单列表 */
.grouped-feedback-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.project-group {
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.project-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #1296DB, #42a5f5);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.project-header:active {
  background: linear-gradient(135deg, #1565c0, #1296DB);
}

.project-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.project-name {
  font-size: 32rpx;
  font-weight: bold;
}

.project-unit {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 2rpx;
}

.expand-icon {
  font-size: 24rpx;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.expand-icon.expanded {
  transform: rotate(-90deg);
}

.task-list {
  background: #f8f9fa;
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
  opacity: 0;
}

.task-list.expanded {
  max-height: none; /* 移除高度限制，允许内容完全展开 */
  opacity: 1;
}

.task-list.collapsed {
  max-height: 0;
  opacity: 0;
}

.task-group {
  border-bottom: 1rpx solid #e0e0e0;
}

.task-group:last-child {
  border-bottom: none;
}

.task-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1rpx solid #f0f0f0;
}

.task-header:active {
  background: #f5f5f5;
}

.task-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.task-number {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.part-name {
  font-size: 24rpx;
  color: #666;
}

.feedback-count {
  font-size: 22rpx;
  color: #1296DB;
  font-weight: 500;
}

/* 反馈单列表容器动画 */
.feedback-list-container {
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.4s ease-in-out, opacity 0.3s ease-in-out;
  opacity: 0;
}

.feedback-list-container.expanded {
  max-height: none; /* 移除高度限制，允许反馈单列表完全展开 */
  opacity: 1;
}

.feedback-list-container.collapsed {
  max-height: 0;
  opacity: 0;
}

.task-list .feedback-list {
  background: #f8f9fa;
  padding: 0 20rpx 20rpx 20rpx;
}

.task-list .feedback-item {
  margin-bottom: 15rpx;
  border-radius: 8rpx;
  transform: translateY(10rpx);
  opacity: 0;
  animation: feedbackItemSlideIn 0.3s ease-out forwards;
}

.task-list .feedback-item:last-child {
  margin-bottom: 0;
}

/* 为反馈单项添加延迟动画，创建错落有致的效果 */
.task-list .feedback-item:nth-child(1) { animation-delay: 0.1s; }
.task-list .feedback-item:nth-child(2) { animation-delay: 0.15s; }
.task-list .feedback-item:nth-child(3) { animation-delay: 0.2s; }
.task-list .feedback-item:nth-child(4) { animation-delay: 0.25s; }
.task-list .feedback-item:nth-child(5) { animation-delay: 0.3s; }
.task-list .feedback-item:nth-child(n+6) { animation-delay: 0.35s; }

/* 反馈单项滑入动画 */
@keyframes feedbackItemSlideIn {
  from {
    transform: translateY(10rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 没有反馈单时的提示 */
.no-feedback-tip {
  padding: 30rpx;
  text-align: center;
  background: #f8f9fa;
  border-radius: 8rpx;
  margin: 15rpx 0;
}

.tip-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.btn-small {
  padding: 16rpx 32rpx;
  font-size: 24rpx;
}

/* 现场信息反馈记录列表 */
.feedback-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feedback-item {
  background: white;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  position: relative;
}

.feedback-info {
  flex: 1;
  padding: 30rpx;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;
}

.feedback-info:active {
  transform: scale(0.98);
}



.btn-small {
  padding: 16rpx 24rpx;
  font-size: 24rpx;
}



.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.feedback-time {
  flex: 1;
}

.time-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.feedback-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  width: 100%;
}

/* 反馈单中的工程信息 */
.feedback-content .project-info {
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
  border-left: 4rpx solid #1296DB;
}

.feedback-content .project-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.feedback-content .project-unit {
  font-size: 24rpx;
  color: #666;
}

/* 反馈类别信息 */
.category-info {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-bottom: 8rpx;
}

.category-label {
  font-size: 24rpx;
  color: #666;
}

.category-name {
  font-size: 24rpx;
  font-weight: 500;
}

.feedback-user-info {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.feedback-label {
  font-size: 24rpx;
  color: #666;
}

.feedback-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}

.notes {
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #ffc107;
  width: 100%;
  box-sizing: border-box;
}

.notes-text {
  font-size: 24rpx;
  color: #555;
  line-height: 1.5;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  width: 100%;
  display: block;
}

.media-info {
  display: flex;
  align-items: center;
}

.media-count {
  font-size: 22rpx;
  color: #666;
}

.arrow {
  font-size: 32rpx;
  color: #ccc;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.empty-btn {
  width: 300rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 12rpx;
  width: 80%;
  max-width: 600rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
  text-align: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-body {
  padding: 30rpx;
  text-align: center;
}

.modal-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.modal-warning {
  font-size: 24rpx;
  color: #f44336;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.modal-footer .btn {
  flex: 1;
  border-radius: 0;
  border-right: 1rpx solid #eee;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
}

.modal-footer .btn:last-child {
  border-right: none;
}

/* 加载更多状态样式 */
.load-more-state {
  padding: 30rpx;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
}

.loading-more-text {
  font-size: 26rpx;
  color: #666;
}

.no-more {
  padding: 20rpx 0;
}

.no-more-text {
  font-size: 24rpx;
  color: #999;
}

