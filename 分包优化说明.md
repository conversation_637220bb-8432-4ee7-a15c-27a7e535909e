# 微信小程序分包优化说明

## 优化前后对比

### 优化前
- **主包大小**: 546KB
- **主包页面数**: 16个页面
- **分包数量**: 3个
- **问题**: 主包过大，包含过多功能页面

### 优化后
- **主包大小**: 421KB (减少125KB，约23%的优化)
- **主包页面数**: 5个页面
- **分包数量**: 4个
- **优化效果**: 主包大小显著减少，加载速度提升

## 分包结构调整

### 主包 (421KB)
保留核心页面：
- `pages/index/index` - 首页
- `pages/Login/Login` - 登录页
- `pages/customer/customer` - 客户页面
- `pages/supplier/supplier` - 供应商页面
- `pages/xiangtong/xiangtong` - 翔通页面

### 订单管理分包 (59KB)
新建分包 `subpackages/order-management`：
- `order/order` - 订单列表
- `orderDetail/orderDetail` - 订单详情
- `orderNew/orderNew` - 新建订单
- `orderUpdate/orderUpdate` - 更新订单
- `orderCarLeaderApprove/orderCarLeaderApprove` - 车辆主管审批
- `orderChoseRepair/orderChoseRepair` - 选择维修人员
- `orderScanCode/orderScanCode` - 扫码功能

### 反馈管理分包 (206KB)
扩展分包 `subpackages/feedback-management`：
- `feedback/feedback` - 反馈页面
- `feedback-detail/feedback-detail` - 反馈详情
- `camera-capture/camera-capture` - 相机捕获
- `fbindex/fbindex` - 反馈首页
- `feedback-list/feedback-list` - 反馈列表

### 项目管理分包 (11KB)
保持 `subpackages/project-management`：
- `project-list/project-list` - 项目列表

### 任务管理分包 (18KB)
保持 `subpackages/task-management`：
- `task-list/task-list` - 任务列表

## 路径更新

### 主包到分包的跳转
```javascript
// 首页跳转到订单管理
wx.navigateTo({
    url: '/subpackages/order-management/order/order'
});

// 首页跳转到反馈管理
wx.navigateTo({
    url: '/subpackages/feedback-management/fbindex/fbindex'
});
```

### 分包内部跳转
```javascript
// 订单分包内部跳转（相对路径）
wx.navigateTo({
    url: '../orderDetail/orderDetail'
});
```

### 分包到主包的跳转
```javascript
// 返回首页
wx.switchTab({
    url: '/pages/index/index'
});
```

## 性能优化配置

### 分包预加载
在 `app.json` 中配置预加载规则：
```json
"preloadRule": {
    "pages/index/index": {
        "network": "all",
        "packages": ["order", "feedback"]
    }
}
```

### 打包忽略配置
在 `project.config.json` 中配置忽略文件：
```json
"packOptions": {
    "ignore": [
        "server/**/*",
        "scripts/**/*",
        "*.md",
        "*.txt",
        ".git/**/*",
        ".vscode/**/*",
        "node_modules/**/*"
    ]
}
```

## 注意事项

1. **路径更新**: 所有页面间的跳转路径已更新为正确的分包路径
2. **懒加载**: 已启用 `lazyCodeLoading: "requiredComponents"`
3. **预加载**: 配置了关键分包的预加载，提升用户体验
4. **包大小监控**: 建议定期检查各分包大小，避免单个分包过大

## 后续建议

1. **继续优化**: 如果反馈管理分包(206KB)仍然较大，可考虑进一步拆分
2. **图片优化**: 检查 `static` 目录中的图片资源，考虑压缩或使用CDN
3. **代码分割**: 对于大型工具函数，可考虑按需加载
4. **监控工具**: 使用微信开发者工具的分包分析功能定期检查

## 测试建议

1. 在真机上测试所有页面跳转是否正常
2. 验证分包加载性能
3. 检查首次启动时间是否有改善
4. 确认所有功能模块正常工作
