// pages/order/order.js
const requestOrder = require('../../request/order')
const utils = require('../../utils/util');
const config = require('../../config/common');
Page({


    data: {
        /**
         * 页面的初始数据
         */
        userId: "",
        mainCompanyId: "",
        mainRoleId: "",
        currentPage: 1,
        clickvalue: 0,
        WxOperaPersonId: "",
        //导航栏tab
        FixStateTab: [{ value: 0, name: '申报中', nvBackcolor: "rgb(18, 150, 219)", nvcolor: "white" },
        { value: 1, name: '待修中', nvBackcolor: "white", nvcolor: "black" },
        { value: 2, name: '维修中', nvBackcolor: "white", nvcolor: "black" },
        { value: 3, name: '维修完成', nvBackcolor: "white", nvcolor: "black" },
        { value: 4, name: '已验收', nvBackcolor: "white", nvcolor: "black" },
        { value: 5, name: '全部订单', nvBackcolor: "white", nvcolor: "black" }],
        //单据编号，单据日期，申请人Id，申请人名称，维修人Id，维修人名称，车辆Id，车辆名称，维修状态
        FromList: []
    },

    //导航栏点击事件
    onNaTabClick(e) {
        if (e.target.dataset.item.nvBackcolor == "white") {
            let tempTab = this.data.FixStateTab;
            tempTab.forEach((param) => {
                if (e.target.dataset.item.value == param.value) {
                    param.nvBackcolor = "rgb(18, 150, 219)";
                    param.nvcolor = "white";
                    this.data.clickvalue = e.target.dataset.item.value;
                    let FuzzyStr = {
                        FixState: param.value,
                        CompId: this.data.mainCompanyId
                    };
                    //如果是司机，只能看到自己的单
                    if (this.data.mainRoleId == config.SJRoleId) {
                        FuzzyStr.WxOperaPersonId = this.data.WxOperaPersonId;
                    }
                    //如果是CDZRoleId搅拌车管理，只能看到搅拌车
                    else if (this.data.mainRoleId == config.JBCRoleId) {
                        FuzzyStr.CarUseId = "10;20;30;40";
                    }
                    //如果是泵车管理，只能看到泵车
                    else if (this.data.mainRoleId == config.BCRoleId) {
                        FuzzyStr.CarUseId = "50;60";
                    }
                    this.data.currentPage = 1;
                    this.setFromList('X_CarServApply', this.data.currentPage, FuzzyStr);
                }
                else {
                    param.nvBackcolor = "white";
                    param.nvcolor = "black";
                }
            });
            this.setData({
                FixStateTab: tempTab
            })
        }
    },
    setFromList(ProgId, PageIndex, FuzzyStr) {
        if (FuzzyStr.FixState > 4) {
            delete FuzzyStr.FixState;
        }
        FuzzyStr.TypeId = config.TypeId;
        requestOrder.GetServerApplyList({
            ProgId: ProgId,
            FuzzyStr: FuzzyStr,
            PageSize: 10,
            PageIndex: PageIndex,
        }).then(res => {
            let templist = [];
            if (res.data.status == "suc") {
                templist = res.data.data;
            }
            this.setData({
                FromList: templist
            })
        }).catch(err => {
            console.log('err', err);
        })
    },
    addFromList(ProgId, PageIndex, FuzzyStr) {
        if (FuzzyStr.FixState > 4) {
            delete FuzzyStr.FixState;
        }
        FuzzyStr.TypeId = config.TypeId;
        PageIndex = PageIndex + 1;
        requestOrder.GetServerApplyList({
            ProgId: ProgId,
            FuzzyStr: FuzzyStr,
            PageSize: 10,
            PageIndex: PageIndex
        }).then(res => {
            let templist = this.data.FromList;
            if (res.data.status == "suc") {
                templist = templist.concat(res.data.data);
            }
            this.setData({
                currentPage: PageIndex,
                FromList: templist
            })
        }).catch(err => {
            console.log('err', err);
        })

    },
    onNewOrderclick(e) {
        wx.navigateTo({
            url: '../orderNew/orderNew',
        })
    },
    //根据角色判断查询条件
    SelectRole(FuzzyStr) {

    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {

    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        utils.getStorage('userInfo').then(res => {
            let tempCompanyID = res.Company.find((item) => {
                return item.IsMain == true;
            });
            let tempMaxRole = 0;
            res.Roles.forEach((item) => {
                if (tempMaxRole <= item.RoleId) {
                    tempMaxRole = item.RoleId;
                }
            })
            this.setData({
                userId: res.userId,
                mainCompanyId: tempCompanyID.CompanyId,
                mainRoleId: tempMaxRole,
                WxOperaPersonId: res.PersonId
            })
            let FuzzyStr = {
                FixState: 0,
                CompId: tempCompanyID.CompanyId,
                TypeId: config.TypeId
            };
            //如果是司机，只能看到自己的单
            if (tempMaxRole == config.SJRoleId) {
                FuzzyStr.WxOperaPersonId = this.data.WxOperaPersonId;
            }
            //如果是CDZRoleId搅拌车管理，只能看到搅拌车
            else if (tempMaxRole == config.JBCRoleId) {
                FuzzyStr.CarUseId = "10;20;30;40";
            }
            //如果是泵车管理，只能看到泵车
            else if (tempMaxRole == config.BCRoleId) {
                FuzzyStr.CarUseId = "50;60";
            }
            this.data.currentPage = 1;
            this.setFromList('X_CarServApply', this.data.currentPage, FuzzyStr);
        }).catch(err => {
            wx.switchTab({
                url: '/pages/index/index',
            })
        })
        this.setData({
            FixStateTab: [{ value: 0, name: '申报中', nvBackcolor: "rgb(18, 150, 219)", nvcolor: "white" },
            { value: 1, name: '待修中', nvBackcolor: "white", nvcolor: "black" },
            { value: 2, name: '维修中', nvBackcolor: "white", nvcolor: "black" },
            { value: 3, name: '维修完成', nvBackcolor: "white", nvcolor: "black" },
            { value: 4, name: '已验收', nvBackcolor: "white", nvcolor: "black" },
            { value: 5, name: '全部订单', nvBackcolor: "white", nvcolor: "black" }]
        })

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
        let FuzzyStr = {
            FixState: this.data.clickvalue,
            CompId: this.data.mainCompanyId,
            TypeId: config.TypeId
        };
        if (this.data.mainRoleId == config.SJRoleId) {
            FuzzyStr.WxOperaPersonId = this.data.WxOperaPersonId;
        }
        this.addFromList('X_CarServApply', this.data.currentPage, FuzzyStr);
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})