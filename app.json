{"pages": ["pages/index/index", "pages/Login/Login", "pages/customer/customer", "pages/supplier/supplier", "pages/xiangtong/xiangtong"], "permission": {"scope.userLocation": {"desc": "您的位置信息将用于记录反馈位置"}}, "requiredPrivateInfos": ["getLocation"], "subpackages": [{"root": "subpackages/order-management", "name": "order", "pages": ["order/order", "orderDetail/orderDetail", "orderNew/orderNew", "orderUpdate/orderUpdate", "orderCarLeaderApprove/orderCarLeaderApprove", "orderChoseRepair/orderChoseRepair", "orderScanCode/orderScanCode"]}, {"root": "subpackages/feedback-management", "name": "feedback", "pages": ["feedback/feedback", "feedback-detail/feedback-detail", "camera-capture/camera-capture", "fbindex/fbindex", "feedback-list/feedback-list"]}, {"root": "subpackages/project-management", "name": "project", "pages": ["project-list/project-list"]}, {"root": "subpackages/task-management", "name": "task", "pages": ["task-list/task-list"]}], "preloadRule": {"pages/index/index": {"network": "all", "packages": ["order", "feedback"]}}, "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#1296db", "navigationBarTitleText": "厦门路桥翔通", "navigationBarTextStyle": "white", "enablePullDownRefresh": true, "onReachBottomDistance": 30}, "lazyCodeLoading": "requiredComponents", "style": "v2", "usingComponents": {"modal": "/components/modal/modal"}, "sitemapLocation": "sitemap.json"}