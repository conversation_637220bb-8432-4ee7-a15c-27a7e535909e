/**index.wxss**/
page{
    background-color: rgb(223, 223, 223);
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 200px;
}

.head{
    padding-top:50rpx;
    height: 35vh;
    background-color: rgb(18, 150, 219);
    text-align:center;
    color: white;
    font-size: large;
    font-weight: bolder;
}

.user-avatar
{
    width: 200rpx;
    height: 200rpx;
    border-radius: 50%;
    text-align: center;
    background-color: rgb(233, 233, 233);
}
.head-Nameinfo
{
    margin: 25rpx;
}
.mybody{
    margin-top: 20rpx;
    margin-left: 5%;
    width: 90%;
    background-color: white;
    font-size: large;
    padding-top:30rpx;
    padding-left:10rpx;
    padding-bottom: 30rpx;
}
.radioinfo
{
    display: flex;
}
.radioinfo view
{
    margin-top: 30rpx;
}

/* 功能选择区域样式 */
.function-section {
    margin: 40rpx 5%;
    background-color: white;
    border-radius: 20rpx;
    padding: 40rpx 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.function-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin-bottom: 40rpx;
}

.button-container {
    display: flex;
    flex-direction: column;
    gap: 30rpx;
}

.function-btn {
    width: 100%;
    font-size: 32rpx;
    color: #1296DB;
}